﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Framework.Exceptions;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using User = PharmaLex.VigiLit.Domain.UserManagement.User;

namespace PharmaLex.VigiLit.Search.Service.Unit.Tests;

public class SearchServiceTests
{
    private readonly ISearchService _searchService;
    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<IReferenceRepository> _mockReferenceRepository = new();
    private readonly Mock<ISubstanceRepository> _mockSubstanceRepository = new();
    private readonly Mock<IClassificationCategoryService> _mockClassificationCategoryService = new();
    private readonly Mock<IPotentialCaseAdditionalFieldService> _mockPotentialCaseAdditionalFieldService = new();
    private readonly Mock<ICompanyRepository> _mockCompanyRepository = new();

    public SearchServiceTests()
    {
        _searchService = new SearchService(
            new NullLoggerFactory(),
            _mockReferenceClassificationRepository.Object,
            _mockReferenceRepository.Object,
            _mockSubstanceRepository.Object,
            _mockClassificationCategoryService.Object,
            _mockPotentialCaseAdditionalFieldService.Object,
            _mockCompanyRepository.Object);
    }

    [Fact]
    public async Task PrintPreview_ReturnsDefaultValues_IfNoFiltersSelected()
    {
        // Arrange
        var request = GetDefaultRequest();
        var user = new User("FirstName", "LastName", "EmailAddress");

        // Act
        var result = await _searchService.PrintPreview(request, user, 5000);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("", result.Filters.Term);
        Assert.Null(result.Filters.CreatedFrom);
        Assert.Null(result.Filters.CreatedTo);
        Assert.Null(result.Filters.LastUpdatedFrom);
        Assert.Null(result.Filters.LastUpdatedTo);
        Assert.Empty(result.Filters.Substances);
        Assert.Empty(result.Filters.Categories);
        Assert.Equal("", result.Filters.PSUR);
        Assert.Equal("", result.Filters.Company);
        Assert.Equal("", result.Filters.Title);
        Assert.Equal("", result.Filters.MeshTerms);
        Assert.False(result.Filters.MeshTermsOr);
        Assert.Equal("", result.Filters.Keywords);
        Assert.False(result.Filters.KeywordsOr);
    }

    [Fact]
    public async Task PrintPreview_ReturnsExpectedValues_IfAllFiltersSelected()
    {
        // Arrange
        var request = new ClassificationSearchRequest()
        {
            Term = "my term",
            CreatedFrom = new DateTime(2001, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            CreatedTo = new DateTime(2002, 2, 2, 0, 0, 0, DateTimeKind.Utc),
            LastUpdatedFrom = new DateTime(2003, 3, 3, 0, 0, 0, DateTimeKind.Utc),
            LastUpdatedTo = new DateTime(2004, 4, 4, 0, 0, 0, DateTimeKind.Utc),
            Substances = new MultiSelectFilterRequest() { SelectedIds = new List<int>() { 1, 2 } },
            ClassificationCategories = new MultiSelectFilterRequest() { SelectedIds = new List<int>() { 1, 2 } },
            SpecialSituations = new MultiSelectFilterRequest() { SelectedIds = new List<int>() { 1, 2 } },
            PSUR = "my psur",
            CompanyId = 1,
            Title = "my title",
            MeshTerm = "my mesh",
            SearchMeshTermWithOr = true,
            Keyword = "my keyword",
            SearchKeywordWithOr = true
        };

        var user = new User("FirstName", "LastName", "EmailAddress");

        var substances = new List<SubstanceModel>
        {
            new() {Id=1,Name="substance 1"},
            new() {Id=2,Name="substance 2"},
        };

        var categories = new List<ClassificationCategoryModel>
        {
            new() {Id=1,Name="category 1"},
            new() {Id=2,Name="category 2"},
        };

        var company = new Company("my company", "", "", true);

        _mockSubstanceRepository
            .Setup(x => x.GetAllAsync())
            .ReturnsAsync(substances);

        _mockClassificationCategoryService
            .Setup(x => x.GetAllAsync())
            .ReturnsAsync(categories);

        _mockCompanyRepository
            .Setup(x => x.GetByIdAsync(1))
            .ReturnsAsync(company);

        // Act
        var result = await _searchService.PrintPreview(request, user, 5000);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("my term", result.Filters.Term);
        Assert.Equal("1 Jan 2001", result.Filters.CreatedFrom?.ToString("d MMM yyyy"));
        Assert.Equal("2 Feb 2002", result.Filters.CreatedTo?.ToString("d MMM yyyy"));
        Assert.Equal("3 Mar 2003", result.Filters.LastUpdatedFrom?.ToString("d MMM yyyy"));
        Assert.Equal("4 Apr 2004", result.Filters.LastUpdatedTo?.ToString("d MMM yyyy"));
        Assert.Equal("substance 1, substance 2", string.Join(", ", result.Filters.Substances.ToArray()));
        Assert.Equal("category 1, category 2", string.Join(", ", result.Filters.Categories.ToArray()));
        Assert.Equal("my psur", result.Filters.PSUR);
        Assert.Equal("my company", result.Filters.Company);
        Assert.Equal("my title", result.Filters.Title);
        Assert.Equal("my mesh", result.Filters.MeshTerms);
        Assert.True(result.Filters.MeshTermsOr);
        Assert.Equal("my keyword", result.Filters.Keywords);
        Assert.True(result.Filters.KeywordsOr);
    }

    [Fact]
    public async Task PrintPreview_ReturnsSearchResults()
    {
        // Arrange
        var request = GetDefaultRequest();

        var user = new User("FirstName", "LastName", "EmailAddress");

        var models = new List<PrintPreviewSearchResultModel>
        {
            new() {Id=1, Title="my title 1"},
            new() {Id=2, Title="my title 2"},
        };

        _mockReferenceClassificationRepository
            .Setup(x => x.PrintPreview(request, user, 5000))
            .ReturnsAsync(models);

        // Act
        var result = await _searchService.PrintPreview(request, user, 5000);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Classifications.Count());
        Assert.Equal("my title 1", result.Classifications.ToList()[0].Title);
        Assert.Equal("my title 2", result.Classifications.ToList()[1].Title);
    }

    [Fact]
    public async Task ClassificationSearch_ReturnsSearchResults()
    {
        // Arrange
        var request = GetDefaultRequest();

        var user = new User("FirstName", "LastName", "EmailAddress");

        var models = new List<ReferenceClassificationSupportModel>
        {
            new() {Id=1, Title="my title 1"},
            new() {Id=2, Title="my title 2"},
        };

        _mockReferenceClassificationRepository
            .Setup(x => x.Search(request, user, 100))
            .ReturnsAsync(models);

        // Act
        var results = await _searchService.ClassificationSearch(request, user, 100);

        // Assert
        Assert.NotNull(results);
        Assert.Equal(2, results.Classifications.Count());
        Assert.Equal("my title 1", results.Classifications.ToList()[0].Title);
        Assert.Equal("my title 2", results.Classifications.ToList()[1].Title);
    }

    [Fact]
    public async Task ClassificationSearch_Handles_FullText_SqlExceptions()
    {
        // Arrange
        var request = GetDefaultRequest();

        var user = new User("FirstName", "LastName", "EmailAddress");

        var sqlException = SqlExceptionFactory.CreateSqlException(7630, "my full-text sql exception");

        _mockReferenceClassificationRepository
            .Setup(x => x.Search(request, user, 100))
            .ThrowsAsync(sqlException);

        // Act
        var results = await _searchService.ClassificationSearch(request, user, 100);

        // Assert
        Assert.NotNull(results);
        Assert.Null(results.Classifications);
        Assert.Equal("my full-text sql exception", results.FullTextErrorMessage);
    }

    [Fact]
    public async Task ClassificationSearch_Throws_Other_SqlExceptions()
    {
        // Arrange
        var request = GetDefaultRequest();

        var user = new User("FirstName", "LastName", "EmailAddress");

        var sqlException = SqlExceptionFactory.CreateSqlException(123, "my other sql exception");

        _mockReferenceClassificationRepository
            .Setup(x => x.Search(request, user, 100))
            .ThrowsAsync(sqlException);

        // Act
        // Assert
        await Assert.ThrowsAsync<SqlException>(async () => await _searchService.ClassificationSearch(request, user, 100));
    }

    [Fact]
    public async Task ReferenceSearch_ReturnsSearchResults()
    {
        // Arrange
        var user = new User("FirstName", "LastName", "EmailAddress");

        var models = new List<ReferenceSupportModel>
        {
            new() {Id=1, Title="my title 1"},
            new() {Id=2, Title="my title 2"},
        };

        _mockReferenceRepository
            .Setup(x => x.Search("my term", user))
            .ReturnsAsync(models);

        // Act
        var results = await _searchService.ReferenceSearch("my term", user);

        // Assert
        Assert.NotNull(results);
        Assert.Equal(2, results.Count());
        Assert.Equal("my title 1", results.ToList()[0].Title);
        Assert.Equal("my title 2", results.ToList()[1].Title);
    }

    [Fact]
    public async Task ReferenceSearch_Rethrows_Exception()
    {
        // Arrange
        var user = new User("FirstName", "LastName", "EmailAddress");

        _mockReferenceRepository
            .Setup(x => x.Search("my term", user))
            .ThrowsAsync(new Exception("my exception"));

        // Act
        // Assert
        await Assert.ThrowsAsync<Exception>(async () => await _searchService.ReferenceSearch("my term", user));
    }

    private static ClassificationSearchRequest GetDefaultRequest()
    {
        return new ClassificationSearchRequest()
        {
            Term = "",
            CreatedFrom = null,
            CreatedTo = null,
            LastUpdatedFrom = null,
            LastUpdatedTo = null,
            Substances = new MultiSelectFilterRequest() { SelectedIds = new List<int>() },
            ClassificationCategories = new MultiSelectFilterRequest() { SelectedIds = new List<int>() },
            SpecialSituations = new MultiSelectFilterRequest() { SelectedIds = new List<int>() },
            PSUR = "",
            CompanyId = 0,
            Title = "",
            MeshTerm = "",
            SearchMeshTermWithOr = false,
            Keyword = "",
            SearchKeywordWithOr = false
        };
    }
}

