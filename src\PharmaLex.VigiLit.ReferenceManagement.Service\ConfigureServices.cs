﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;
using PharmaLex.VigiLit.ReferenceManagement.Contracts.Security;
using PharmaLex.VigiLit.ReferenceManagement.Service.Access;

namespace PharmaLex.VigiLit.ReferenceManagement.Service;
public static class ConfigureServices
{
    public static void RegisterReferenceManagement(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IImportingReferenceClassificationRepository, ReferenceClassificationRepository>();
        services.AddScoped<IReferenceClassificationRepository, ReferenceClassificationRepository>();

        services.AddScoped<IImportingReferenceRepository, ReferenceRepository>();
        services.AddScoped<IReferenceRepository, ReferenceRepository>();

        services.AddScoped<IReferenceUpdateRepository, ReferenceUpdateRepository>();
        services.AddScoped<IImportingReferenceUpdateRepository, ReferenceUpdateRepository>();

        services.AddScoped<IPotentialCaseAdditionalFieldService, PotentialCaseAdditionalFieldService>();
        services.AddScoped<IPotentialCaseAdditionalFieldRepository, PotentialCaseAdditionalFieldRepository>();

        services.AddScoped<IReferenceManager, ReferenceManager>();

        services.AddScoped<IPermissionEvaluator, ViewReferenceEvaluator>();
        services.AddScoped<IPermissionEvaluator<ViewReferencePermissionContext>, ViewReferenceEvaluator>();
        services.AddScoped<IPermissionEvaluator, ViewReferenceClassificationEvaluator>();
        services.AddScoped<IPermissionEvaluator<ViewReferenceClassificationPermissionContext>, ViewReferenceClassificationEvaluator>();
    }
}
