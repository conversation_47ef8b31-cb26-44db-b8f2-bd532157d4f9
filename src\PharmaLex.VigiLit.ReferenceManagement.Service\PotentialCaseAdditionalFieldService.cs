﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ReferenceManagement.Service;

internal class PotentialCaseAdditionalFieldService(IPotentialCaseAdditionalFieldRepository potentialCaseAdditionalFieldRepository,
                                                 IMapper mapper)
                                                 : IPotentialCaseAdditionalFieldService
{
    public async Task<IEnumerable<PotentialCaseAdditionalFieldModel>> GetAllAsync()
    {
        var res = await potentialCaseAdditionalFieldRepository.GetAllAsync();
        return mapper.Map<IEnumerable<PotentialCaseAdditionalFieldModel>>(res);
    }
}