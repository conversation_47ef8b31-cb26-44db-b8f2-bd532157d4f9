﻿using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Search.Service;

public interface ISearchService
{
    Task<ReferenceClassificationSearchResultsModel> ClassificationSearch(ClassificationSearchRequest request, User user, int? maxRows);
    Task<IEnumerable<ReferenceSupportModel>> ReferenceSearch(string term, User user);
    Task<PrintPreviewPageModel> PrintPreview(ClassificationSearchRequest request, User user, int? maxRows);
}
