﻿input:disabled, select:disabled {
    opacity: 0.7;
}

select option.inactive {
    background: #DDD;
}

.buttons {
    margin-top: 1rem;
}

table thead {
    background-color: white;
    border-bottom: none;
}

table tr th {
    padding: 0.6rem 0.75rem;
}

table tr.selectable:hover td {
    background-color: #e9e9e9;
}

.pager {
    background-color: white;
    border-radius: 0.6rem;

    .page-size {
        label {
            margin-bottom: 0;
        }
    }
}

table tr td {
    vertical-align: middle;
}

table {
    border-bottom: solid 2px #f0f7f7;
}

table tr:last-of-type {
    border-bottom: none;
}

table tbody tr:nth-child(odd) {
    background: $grey-ultra-light;
}

table th,
table td {
    padding: 0.6rem 0.75rem;
}

.data-table-actions {
    text-align: right;
}

.data-table-actions div {
    float: right;
}

.dataTable th {
    overflow: visible;
}

.input-validation-error {
    border: 1px solid #8f0101 !important;
}

span.validation-error {
    padding: 0;

    span {
        font-size: 1rem;
        color: #8f0101;
    }
}

.page-size {
    flex: 0 240px !important;
}

.tabs {
    display: flex;
    border-bottom: 2px solid $grey-4;
    margin-bottom: 1rem;

    li {
        margin-bottom: -2px;
        margin-right: 1em;
        cursor: pointer;
        vertical-align: middle;
        text-align: center;
        color: $grey-1;
        padding: 5px 0 10px 0;

        span {
            vertical-align: middle;

            &:first-child {
                margin-right: 7px;
            }

            &:hover {
                color: $brand;
                transition-duration: 0.5s;
            }
        }

        a {
            vertical-align: middle;
            margin-right: 7px;
            color: $grey-1;

            &:hover {
                color: $brand;
            }
        }

        &.active {
            color: $brand;
            padding-bottom: -2px;
            border-bottom: 2px solid $brand;
            cursor: default;

            a {
                color: $brand-darker;

                &:hover {
                    color: $brand;
                }
            }
        }

        &.separator {
            display: inline-block;
            border-right: 2px solid $grey-3;
            cursor: default;
            margin-top: 10px;
            margin-left: 3px;
        }

        &:last-of-type {
            margin-bottom: -2px;
        }
    }

    .tab-badge {
        display: inline-block;
        line-height: 1rem;
        margin: 1px auto;
        text-align: center;
        border-radius: 15px;
        color: $white;
        background-color: $reg;
        padding: 5px 10px 4px;

        &.completed {
            background-color: $brand;
        }

        &:hover {
            color: $white;
        }
    }

    li.nolink {
        cursor: default;

        span {
            &:hover {
                color: $grey-1;
            }

            &.tab-badge {
                color: $white;
            }
        }
    }
}

.tabs.borderless {
    border: none;

    li.active {
        border: none;
    }
}

.field-validation-error {
    color: #8f0101;
    padding-left: 0.8rem;
    line-height: 1.6rem;
}

.sub-nav {
    height: 35px;
    padding-top: 8px;
    padding-left: 10px;
}

.sub-nav li {
    float: left;
    margin-right: 22px;
}

.sub-nav a {
    color: #727272;
    cursor: pointer;
    text-decoration: none;

    &:hover {
        color: $brand;
        transition-duration: 0.5s;
    }
}

.sub-nav a.active {
    color: $grey-dark;
    border-bottom: 2px solid $brand;
}

.sub-layout-header-buttons {
    margin-bottom: 1rem;
}

.core-content.has-background {
    background-image: url(/src/images/splash.jpg);
}

.z-index-1 {
    z-index: 1;
}

.chip {
    border-radius: 15px;
    background-color: $blue-600;
    color: #fff;
    height: 25px;
    display: inline-block;
    font-weight: bold;
    margin: 0 10px 10px 0;
    padding: 0 0 0 10px;
    vertical-align: middle;
}

.chip span {
    position: relative;
    top: 1px;
    padding-right: 2px;
}

.chip i {
    position: relative;
    top: 5px;
    font-size: 1rem;
    margin-right: -10px;
}

.sub-header {
    margin-left: -2rem;
    margin-right: -2rem;
    margin-bottom: 1rem;
    position: sticky;
    top: -1rem;
    z-index: 99;
}

.core-content {
    padding-left: 30px;
    padding-right: 1.875rem;
    padding-top: 0;
}

.core-content section.card-container {
    background: none;
    padding-left: 0;
    padding-right: 0;

    .section-card {
        border-radius: 4px;
        flex-basis: 30%;
        background-color: $white;
        position: relative;
        padding: 1.5rem;
    }

    .section-card.expand {
        @media screen and (max-width: 1200px) {
            flex-basis: 40%;
        }
    }
}

.flex-gap {
    gap: 1rem;
}

.flex-align-center label {
    margin-bottom: 0rem;
}

.version-label {
    background: $brand;
    color: $white;
    padding: $padding-unit 1rem;
    border-radius: 4px;
}

.full-width-rule {
    margin: 0 -1.5rem;
}

.switch-container.switch-active-inactive {
    user-select: none;

    input.switch {
        margin: 0;
    }

    label.switch {
        text-indent: unset;
        color: $white;
        font-weight: 400;
        width: 80px;
        text-align: right;
        padding-top: 6px;
        padding-right: $padding-unit;
        margin: 0;

        &:before {
            content: "Inactive";
        }
    }

    input.switch:checked + label {
        &:after {
            transform: translateX(200%);
        }

        &:before {
            content: "Active";
            position: absolute;
            left: 0.75rem;
        }
    }
}

.switch-container.switch-enabled-disabled {
    user-select: none;
    margin-top: .75rem;

    input.switch {
        margin: 0;
    }

    label.switch {
        text-indent: unset;
        color: $white;
        font-weight: 400;
        width: 80px;
        text-align: right;
        padding-top: 6px;
        padding-right: 0.3rem;
        margin: 0;

        &:before {
            content: "Disabled";
        }
    }

    input.switch:checked + label {
        &:after {
            transform: translateX(200%);
        }

        &:before {
            margin-right: 50px;
            content: "Enabled";
            position: absolute;
            left: 0.45rem;
        }
    }
}
/* Status Lozenge */
[class^="state-indicator-"],
[class*="state-indicator-"] {
    display: inline-block;
    width: 100%;
    line-height: 1.5rem;
    border-radius: 1rem;
    color: $white;
    text-align: center;
    font-size: .75rem;
    min-width: 3.125rem;
    background-color: $brand;
}

.state-indicator-new {
    background-color: $grey-1;
}

.state-indicator-updated {
    background-color: $stats;
    margin-bottom: 5px;
}

.state-indicator-preclassified {
    background-color: $safety;
}

.state-indicator-approved {
    background-color: $success;
}

.state-indicator-reclassified {
    background-color: $yellow-800;
}

.state-indicator-signed {
    background-color: $brand;
}

.state-indicator-inactive {
    background-color: $blue-dark;
}

.support-reference {
    border: 1px solid $grey-3;
    border-radius: 10px;
    padding: 1rem;
}

// Support (History)
.support-container {
    padding: 1rem;
    background: $white;

    li a {
        color: #6c767d;
    }

    li.active a {
        color: #127f92;
    }

    ul li.active {
        color: $brand;
    }
}

.support-lozenge {
    display: flex;
    border-radius: 10px;
    margin-bottom: 1rem;
    cursor: pointer;

    &.selected {
        border: 2px solid $brand;
    }

    &.selectable {
        border: 2px solid $grey-3;
    }

    div.lozenge-container {
        padding: 5px 20px 5px 5px;
    }

    div.action-container {
        padding: 5px 5px 5px 0;
    }

    .version-badge {
        display: inline-block;
        margin: 0.2rem auto;
        width: 2.2rem;
        line-height: 2.2rem;
        text-align: center;
        border-radius: 50%;
        color: $brand-darker;
        background-color: rgba($brand, 0.1);
    }

    [class^="state-indicator-"],
    [class*="state-indicator-"] {
        width: 8rem;
        line-height: 30px;
    }
}

#support-history-modal {

    .modal-container {
        border-radius: 5px;
    }

    .modal-header {
        border: none;
    }

    input {
        height: 10vh;
    }

    .dialog-custom-content div:nth-child(2) {
        align-items: center;
        margin-left: auto;
    }

    a {
        margin: 1.5rem 0 0 0.5rem;
    }

    #dialog-closer {
        display: none;
    }
}

.reference-container {
    display: flex;
    flex-direction: column;

    .reference-group {
        display: flex;
        margin-bottom: 1rem;

        label {
            flex: 1 0 12%;
        }

        span,
        a {
            flex: 1 0 88%;
        }
    }
}

// Pre-classification
.reference-container-row {
    display: flex;
    flex-wrap: wrap;
    border: 1px solid $grey-3;
    border-radius: 0.6rem;
    background-color: #fff;

    > div {
        padding: 1rem;
    }

    .reference-info {
        display: flex;
        flex-direction: column;
        flex: 0 0 280px;
        position: relative;

        .reference-group {
            display: flex;
            flex-wrap: wrap;
            word-break: break-all;

            label {
                flex: 1 0 25%;
            }

            span {
                flex: 1 0 75%;
            }
        }

        .positionBottom {
            position: absolute;
            bottom: 1rem;
            display: block;
            width: 90%;

            label {
                padding-right: 10px;
            }
        }
    }

    .reference-content {
        flex: 5 1 25rem;
        border-right: 1px solid $grey-3;

        .reference-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0;
        }
    }
}

.reference-classification {
    flex: 0 0 350px;
    display: flex;
    flex-direction: column;

    .form-group:last-of-type {
        margin-bottom: 1.5rem;
    }

    &.ai-expanded {
        flex: 0 0 370px;

        .form-group.ai-suggestion, .form-group.ai-status {
            margin-bottom: 10px;
        }
    }

    .dosage-form {
        display: flex;

        div:first-child {
            flex: 1 1 30%;
        }

        .dosage-form-badges {
            flex: 1 1 70%;
            margin-left: 1rem;

            > span {
                display: inline-block;
                min-width: 1.5rem;
                height: 1.5rem;
                background-color: $quality;
                text-align: center;
                color: $white;
                margin: 0 0 0.5rem 0.5rem;
                line-height: 1.5rem;
                padding: 0 1rem;
                border-radius: 0.9rem;

                &:hover {
                    cursor: pointer;
                    opacity: 0.8;
                    transition-duration: 0.2s;
                }
            }
        }

        .dosage-form-badges.disabled {
            > span {
                opacity: 0.5;

                &:hover {
                    cursor: default;
                }
            }
        }
    }

    .ai-status i {
        font-size: 30px;
        width: 30px;
        cursor: default;
        opacity: 1;
    }

    ai-status i:hover {
        opacity: 1;
    }

    .ai-status div {
        margin-left: 5px;
        padding: 6px 12px 6px 12px;
        border-radius: 0px 8px 8px 8px;
    }

    .ai-status-awaiting-response div {
        background: #FFF8BA;
    }

    .ai-status-success div {
        background: #D8FEE7;
    }

    .ai-status-failed div {
        background: #FCD9D1;
    }

    .ai-suggestion {

        &.category.rejected {
            margin-bottom: 19px;
        }

        &.pending > div {
            background: #E7F7FF;
        }

        &.accepted > div {
            background: #D8FEE7;
        }

        &.rejected > div {
            background: #F9F9F9;
        }

        &.pending .header {
            background: #CEEDFC;
        }

        &.accepted .header {
            background: #C4F5D8;
        }

        &.rejected .header {
            background: #F5F5F5;
        }

        .header {
            width: 100%;
            padding: 10px 20px 10px 20px;
            border-radius: 8px 8px 0px 0px;
            border-bottom: 1px solid white;

            .header-text {
                font-weight: bold;
                color: #461E96;
                line-height: 25px;
                width: 240px;
            }

            .header-buttons {
                display: flex;

                .icon-button {
                    display: inline-block;
                    width: 25px;
                    height: 25px;
                    text-align: center;
                    margin-left: 5px;
                    border: 1px solid #CACACA;
                    border-radius: 5px;
                    background: #FFF;

                    &:hover {
                        background: #F5F5F5;
                        cursor: pointer;
                    }

                    i {
                        font-size: 17px;
                        position: relative;
                        left: -2px;
                        top: 3px;

                        &:hover {
                            opacity: 1;
                        }
                    }

                    &.disabled {
                        opacity: 0.5;

                        &:hover {
                            opacity: 0.5;
                            background: #FFF;
                            cursor: default;
                        }

                        i:hover {
                            cursor: default;
                        }
                    }
                }

                .undo-button {
                    display: inline-block;
                    position: relative;
                    padding: 5px;
                    margin-left: 20px;
                    cursor: pointer;
                }
            }
        }

        .suggested-value {
            padding: 15px 20px;
            border-bottom: 1px solid white;

            div:first-child {
                font-weight: bold;
            }

            div:last-child.value-box {
                background: white;
                border-radius: 5px;
                padding: 11px 15px 12px 15px;
                margin-top: 5px;
            }

            div:last-child .dosage-form {
                display: inline-block;

                .dosage-form-badges {
                    margin-left: 0;
                    margin-top: 10px;
                }
            }

            div:last-child select, div:last-child input {
                background: white;
                margin-top: 5px;
            }
        }

        .reasoning {
            padding: 15px 20px;
            border-bottom: 1px solid white;

            div:first-child {
                font-weight: bold;
            }

            div:last-child {
                margin-top: 5px;
            }
        }

        .previous-value {
            padding: 15px 20px;
            border-bottom: 1px solid white;
        }

        & > div:last-child {
            border-radius: 0px 0px 8px 8px;
            padding-bottom: 15px;
        }
    }

    .form-group.actions {
        height: 42px;
        margin-top: auto;
        width: 108%;
        text-align: right;
        border-top: 1px solid $grey-3;
        margin-left: -1rem;
        margin-right: -3rem;
        padding-top: 1rem;

        .preClassifierName {
            float: left;
            text-align: left;
            padding-left: 1rem;
            padding-top: 5px;
        }

        button, div {
            margin: 0 1rem;

            .switch-container {
                position: relative;
                top: -2px;
                right: -8px;

                &:focus {
                    border: 2px solid #111;
                }
            }
        }
    }

    .separated {
        margin: 0 -1rem;
        padding: 1rem;
        border-top: 1px solid $grey-3;
    }
}

.potential-case-additional-fields-ai {
    padding-top: 15px;
    height: 50px;

    .multi-select {
        width: 100%;
    }
}

.potential-case-additional-fields-non-ai {
    padding-top: 15px;
    height: 50px;
    margin-bottom: 40px;

    .multi-select {
        width: 100%;
    }
}

.audit-trail-container {
    position: relative;
    padding: 8px;
}

.audit-trail-menu-heading {
    display: flex;
    justify-content: space-between;
    border: 2px solid #D8D8D8;
    padding: 0.01em 16px;
    border-radius: 5px;
    height: 30px;
}

.audit-trail-menu-heading-text {
    display: grid;
    place-items: center;
}

.audit-trail-dropdown-content {
    display: none;
    border: 2px solid #D8D8D8;
    padding: 0.01em 16px;
    border-radius: 5px;
    position: absolute;
    z-index: 1;
    height: 60px;
    background-color: white;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
}

.audit-trail-dropdown-content li {
    display: inline-block;
    width: auto;
    white-space: nowrap;
}

.audit-trail-dropdown-content > ul > li:hover {
    background-color: #ddd;
    cursor: pointer;
}

.audit-trail-container:hover .audit-trail-dropdown-content {
    display: grid;
    place-items: center;
}

.audit-trail-download-icon-placement {
    display: grid;
    place-items: center;
}


.audit-trail-download-icon {
    font-family: 'MaterialIconsOutlined', sans-serif;
    font-size: 12px;
    color: black;
    padding-right: 10px;
}

.switch-container input.switch:checked + label {
    color: $white;
}

.page-header .main-navigation-wrapper {
    width: 100%;
    display: flex;
}

.flyout-header.account {
    display: flex;
    align-items: center;
    justify-content: center;
}

.horizontal-filter {
    background-color: #EEE;
    padding: 1rem;

    .input-group {
        margin-bottom: 1rem;
    }

    select {
        width: auto;
        background-color: white;
        margin-right: 10px;
    }

    .horizontal-filter-item {
        display: inline-block;
        margin: 0 10px 0 0;
        padding: 0;
        max-width: min-content;

        #btnLabelSearch {
            margin-top: .5rem;
        }

        .reset-button {
            background-color: #737373;
            transition-duration: 0.5s;

            &:hover {
                background-color: #454545;
            }
        }

        select {
            margin: 0;
        }
    }

    i {
        display: block;
        position: absolute;
        left: 0;
        top: 30px;
    }

    &.flex {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
    }
}

abbr[title] {
    border-bottom: none;
    text-decoration: none;
}

// Edit Substance page
#substance {
    #synonyms-column {
        .form-group {
            #addSynonym {
                flex-grow: 1;
            }

            #addBtn {
                margin-left: 10px;
            }
        }

        #table-synonyms {
            border-bottom: none;
            max-width: 600px;
            margin-bottom: 1rem;

            tbody {
                tr {
                    td .m-icon {
                        float: right;
                    }

                    &:nth-child(odd) {
                        background-color: $grey-5;
                    }
                }
            }
        }
    }
}

.table-filter-items {
    input {
        &[type=search] {
            padding-right: 12px;
        }
    }

    .m-icon.close {
        opacity: 1;
    }
}

.emailBounceText {
    height: 280px;
}

.emailSuppressTableText {
    color: $white;
    margin-left: 30px;
    padding: 5px;
    border-radius: 5px;
}

.email-bounce-background {
    background-color: $error;
}

.email-block-background {
    background-color: $warning;
}

.email-spam-background {
    background-color: $grey-1;
}

.email-bounce-foreground {
    color: $error;
}

.email-block-foreground {
    color: $warning;
}

.email-spam-foreground {
    color: $grey-1;
}

#company-user-edit {
    .suppression-reason {
        border: 1px solid #c4c4c4;
        opacity: 0.7;
        border-radius: 0.5rem;
        background: #f3f6f9;
        padding: .75rem;
        margin-bottom: 1.5rem;
        height: 175px;
        overflow-y: scroll;
    }
}


.cursor-not-allowed {
    cursor: not-allowed;
}

.pointer-events-none {
    pointer-events: none;
}

.disabled {
    opacity: 0.7;
}

#dashboard {
    display: flex;
    flex-wrap: wrap;
    column-gap: 25px;
    margin: 15px auto;

    #import-dashboard-container {
        margin-bottom: 30px;

        &.narrow {
            width: 680px;
        }

        &.wide {
            max-width: 1340px;

            &.empty {
                min-width: 680px;
            }
        }

        #import-dashboard {

            .content {
                background: white;
                border-radius: 10px;
                padding: 20px;

                .cards {
                    display: flex;
                    flex-wrap: wrap;
                    column-gap: 20px;
                    row-gap: 10px;

                    .card {
                        border: 1px solid #C4C4C4;
                        border-radius: 10px;
                        padding: 0;
                        width: 310px;
                        line-height: normal;

                        .info {
                            display: flex;
                            flex-wrap: nowrap;
                            width: 100%;
                            padding: 16px 20px;
                            height: 96px;

                            .text {
                                width: 36%;
                                text-align: left;

                                .import-type {
                                    font-size: 17px;
                                    font-weight: bold;
                                    text-transform: uppercase;
                                    width: 200px;

                                    &.scheduled {
                                        color: $blue-dark;
                                    }

                                    &.adhoc {
                                        color: $brand;
                                    }
                                }

                                .import-date {
                                    font-size: 14px;
                                    color: #1F394A;
                                    padding-top: 5px;
                                    padding-bottom: 7px;
                                }

                                .import-status {
                                    font-size: 12px;
                                    font-weight: bold;
                                    color: #000000;
                                }
                            }

                            .number {
                                width: 64%;
                                text-align: right;

                                .todo-count-label {
                                    font-size: 12px;
                                    color: #1F394A;
                                    padding-top: 2px;
                                    padding-bottom: 5px;
                                }

                                .todo-count-number {
                                    font-size: 38px;
                                    font-weight: bold;
                                    color: $blue-400;
                                }
                            }
                        }

                        .buttons {
                            display: flex;
                            flex-wrap: nowrap;
                            border-top: 1px solid #C4C4C4;
                            width: 100%;
                            background: transparent;
                            padding: 10px 20px;
                            margin: 0;

                            button, a.button {
                                padding: 8px 16px;

                                &:focus {
                                    border: 2px solid black !important;
                                    padding: 7px 15px;
                                    transition-duration: 0s;
                                }
                            }

                            .left {
                                text-align: left;
                                width: 30%;
                            }

                            .right {
                                text-align: right;
                                width: 70%;

                                .details-button {
                                    margin-right: 5px;
                                }

                                .details-button, .archive-button {
                                    background: white;
                                    color: black;
                                    border: 1px solid black;

                                    &:hover {
                                        background: #1f394a;
                                        color: white;
                                        transition-duration: 0.2s;
                                    }

                                    &:disabled {
                                        background: white;
                                        color: black;
                                    }
                                }
                            }
                        }

                        &.selected {
                            border: 3px solid $green-500;

                            .info {
                                padding: 14px 18px 16px;
                                height: 94px;

                                .text {
                                    .import-type {
                                        color: #028930;
                                    }
                                }
                            }

                            .buttons {
                                padding: 10px 18px 8px;

                                .deselect-button {
                                    background: #028930;
                                }
                            }
                        }
                    }
                }

                .empty {
                    p {
                        margin: 0;
                    }
                }
            }
        }
    }

    #email-dashboard-container {
        width: 680px;
        margin-bottom: 10px;

        #email-dashboard {

            .content {
                background: white;
                border-radius: 10px;
                padding: 20px;

                .cards {
                    display: flex;
                    flex-wrap: nowrap;
                    column-gap: 20px;
                    margin-bottom: 20px;

                    .card {
                        border: 1px solid #C4C4C4;
                        border-radius: 10px;
                        width: 200px;
                        text-align: center;
                        padding: 40px 30px;
                        line-height: normal;

                        .text {
                            font-size: 16px;
                            font-weight: bold;
                        }

                        .value {
                            font-size: 70px;
                            color: #572F8C;
                            font-weight: bold;
                        }
                    }
                }

                #table-container {
                    border: 1px solid #C4C4C4;
                    border-radius: 10px;

                    table {
                        background: transparent;

                        thead {
                            background: transparent;
                        }

                        td, th {
                            padding: 15px 20px 14px;
                        }
                    }

                    .pager {
                        padding-left: 6px;
                        padding-right: 6px;
                    }
                }
            }
        }

        #build-info {
            margin-top: 30px;

            .content {
                background: white;
                border-radius: 10px;
                padding: 20px;
            }

            p:last-of-type {
                margin-bottom: 0;
            }
        }
    }
}

#reference-details-container {

    #reference-substance-section, #reference-history-section {
        border: solid 1px #c4c4c4;
        border-radius: 5px;
        padding: 20px;

        li:hover {
            color: $brand;
        }
    }

    #substance-list {
        cursor: pointer;
        border: 2px solid #c4c4c4;
        border-radius: 5px;
        padding: 10px;

        span.substance-name {
            font-weight: bold;
        }

        span.plxid {
            display: inline-block;
            padding-top: 5px;
            font-size: 11px;
        }
    }

    .reference-details-section {
        border: solid 1px #c4c4c4;
        border-radius: 5px;
        padding: 0;
    }

    #reference-details-info {
        border-bottom: solid 1px #c4c4c4;
        padding: 10px 10px 0;

        label {
            padding: 10px 6px 10px 10px;
        }

        span {
            margin-right: 16px;
        }
    }

    #reference-details-content {
        padding: 20px;

        .form-group-display {
            display: flex;
            flex-basis: 50%;
            margin-bottom: 1rem;

            label {
                flex-basis: 40%;
            }
        }
    }

    #classification-section {
        border-left: solid 1px #c4c4c4;
        padding: 20px;
        min-width: 250px;

        .tile {
            padding: 0;
        }

        .form-group-display {
            margin-bottom: 1rem;
        }

        .reason-for-change {
            display: block;
            width: 200px;
            word-wrap: break-word;
        }

        .reference-classification {
            flex: none;
            width: 350px;

            label {
                flex-basis: unset;
            }
        }
    }
    /* Reference History actions status styling */

    .state-indicator-update, .state-indicator-silent {
        background-color: $yellow-800;
    }
}

#import-dashboard-details {
    .row {
        border-bottom: 1px solid #EEE;
        margin-bottom: 10px;
    }

    .column {
        float: left;
        width: 33.33%;
    }

    .centered {
        text-align: center
    }
    /* Clear floats after the columns */
    .row:after {
        content: "";
        display: table;
        clear: both;
    }

    .paginator {
        span {
            margin-right: 10px;
        }
    }

    .show-contract-icon {
        font-family: 'MaterialIconsOutlined', sans-serif;
        font-size: 1.60rem;
        cursor: pointer;
        text-decoration: none;
    }
}

#locks {
    #locks-dashboard {
        background-color: unset;
        padding-left: 0;
        padding-right: 0;

        .cards {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            column-gap: 1rem;
            row-gap: 1rem;

            @media screen and (max-width: 1200px) {
                grid-template-columns: repeat(5, 1fr);
            }

            .card {
                padding: 20px 20px 5px 20px;
                border-radius: 4px;
                font-size: 15px;
                width: 100%;
                background: white;

                p {
                    font-weight: 400;
                }

                .flex-container {
                    display: flex;
                    flex-flow: row wrap;
                    width: 100%;
                    margin-top: 30px;

                    .locksCount {
                        width: 50%;
                        margin-bottom: 15px;
                        font-size: 40px;
                    }

                    .release-button {
                        width: 50%;
                        text-align: right;
                        margin-bottom: 15px;

                        button {
                            background-color: $warning;
                            color: $text;

                            &:hover, &:active {
                                background-color: $grey-1;
                            }
                        }
                    }
                }
            }
        }

        p {
            margin-bottom: 0;
        }
    }
}

.no-import-selected {
    padding: 30px 20px 20px;
    margin: 0 auto;
    width: 400px;

    i {
        font-size: 30px;
        display: inline-block;
        padding-right: 40px;
        font-weight: bold;
    }

    h2 {
        display: inline-block;
        margin: 0;
        position: relative;
        top: -5px;
    }
}

.preClassificationCompleted {
    padding: 30px 20px 20px;
    margin: 0 auto;
    width: 400px;

    i {
        font-size: 30px;
        display: inline-block;
        padding-right: 40px;
        font-weight: bold;
    }

    h2 {
        display: inline-block;
        margin: 0;
        font-size: 20px;
        position: relative;
        top: -5px;
    }
}

.pickReturnedNothing {
    padding: 30px 20px 20px;
    margin: 0 auto;
    width: 700px;

    i {
        font-size: 30px;
        display: inline-block;
        padding-right: 40px;
        font-weight: bold;
        position: relative;
        top: -45px;
    }

    h2 {
        display: inline-block;
        margin: 0;
        font-size: 20px;
        position: relative;
        top: -5px;
    }
}

.classification-completed, .classification-signed {
    padding: 30px 20px 20px;
    margin: 0 auto;
    width: 400px;

    i {
        font-size: 30px;
        display: inline-block;
        padding-right: 40px;
        font-weight: bold;
    }

    h2 {
        display: inline-block;
        margin: 0;
        position: relative;
        top: -5px;
    }
}

.new-company-project-header, .case-upload-header {
    display: flex;
    justify-content: space-between;
    padding: 1rem 2rem 0 2rem;
    border-bottom: 1px solid $grey-4;
    align-items: center;

    h2 {
        font-weight: 600;
    }

    .case-status {
        background-color: #026fa8;
        color: #fff;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        margin-top: 0px;
        font-size: 1rem;
        font-weight: 400;
        height: fit-content;

        &.pending {
            background-color: #bb5c20;
        }
    }
}

/* Case Upload Popup */

.case-upload-form-left {
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e5e5e5;
    gap: 1rem;
    width: 40%;
    padding: 1rem 2rem 0 2rem;
}

.case-upload-substance-wrapper .case-upload-label {
    margin-bottom: 0;
}

.case-upload-divider {
    height: 2px;
    border-top: 1px solid #e5e5e5;
    margin: 0.8rem -2rem;

    &.first-substance-divider {
        margin-top: 3px;
    }

    &.last-substance-divider {
        margin-bottom: 3px;
    }
}

.case-upload-form-right {
    padding: 2rem;
}

.case-upload-content {
    height: fit-content;
    position: relative;
    display: flex;
}

.case-upload-input {
    align-self: stretch;
    position: relative;
}

.case-upload-input {
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #c4c4c4;
    box-sizing: border-box;
    height: 32px;
    display: flex;
    flex-direction: row;
    padding: 8px 12px;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    font-size: var(--body-main-body-size);
}

.case-upload-psur-dropdown-wrapper, .case-upload-mlm-dropdown-wrapper {
    display: flex;
    flex-direction: column;
}

.case-upload-mlm-dropdown-wrapper {
    right: 70%;
    left: 17%;
}

.case-upload-comment-wrapper textarea {
    resize: none;
    margin-bottom: 1rem;
}

.case-upload-company-dropdown-wrapper {
    min-height: 50px;
}

.case-upload-company-dropdown-wrapper #CompanyLabelNoPlxId,
.case-upload-company-dropdown-wrapper #CompanyLabelInvalidPlxId,
.case-upload-company-dropdown-wrapper #CompanyLabelNoCompany {
    display: block;
    color: #AD0000;
    font-weight: normal;
}

.case-upload-dropdown-group {
    display: flex;
    gap: 1rem;
    padding-top: 0.5rem;
}

.case-upload-plxid-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    font-family: var(--body-main-body);
    align-self: stretch;
    padding-top: 1rem;
}


.case-upload-substance-wrapper {
    font-family: var(--body-main-body);
}

.case-upload-substance-wrapper label:first-of-type {
    margin-right: 61px;
}

.case-upload-substance-break-top, .case-upload-substance-break-bottom {
    position: absolute;
}

.case-upload-substance-break-top {
    height: 0%;
    width: 40.8%;
    top: 21%;
    left: -1.7%;
    border-top: 1px solid #e5e5e5;
}

.case-upload-substance-break-bottom {
    width: 40.8%;
    top: 14%;
    left: -1.7%;
    border-top: 1px solid #e5e5e5;
}

.case-upload-filedrop-wrapper {
    position: absolute;
    height: 24%;
    width: 56%;
    top: 5%;
    right: 2%;
    bottom: 61%;
    left: 42%;
    text-align: center;
}

.case-upload-file-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: space-between;
    border-bottom: 1px solid $grey-3;
    padding: 0.3rem 0;
    margin: 0;

    &:first-of-type {
        border-top: 1px solid $grey-3;
    }
}

.case-upload-file-options {
    font-family: 'MaterialIconsOutlined', sans-serif;
    font-size: 1.75rem;
    display: flex;
    gap: 0.5rem;
    align-items: center;

    div {
        padding: 0.25rem;
        cursor: pointer;
    }

    div.disabled {
        opacity: 0.5;
        pointer-events: none;
    }

    .case-upload-file-delete {
        padding-bottom: 0.4rem;
        padding-right: 0rem;
    }
}

.flex.flex-nowrap.justify-end i.m-icon {
    margin-left: 3px;
}

.table-row.delete-highlighted {
    background-color: #ffdede;
}

#modal-dialog-case-delete {
    p {
        margin-bottom: 15px;
        font-size: 1rem;
    }

    #dialog-closer {
        position: absolute;
        right: 5px;
        top: 10px;
    }
}

#caseCommentInput {
    min-height: 82px;
}

/* Button Spinner Animation */
.btn .spinner {
    -ms-transform: scale(0.7,0.7); /* IE 9 */
    -webkit-transform: scale(0.7,0.7); /* Safari */
    transform: scale(0.7,0.7);
    position: absolute;
    width: 32px;
    height: 32px;
    top: 50%;
    margin-top: -16px;
    opacity: 0;
    background-image: url( data:image/gif;base64,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);
}

.btn,
.btn .spinner,
.btn .btn-label {
    -webkit-transition: 0.3s cubic-bezier(0.175, 0.885, 0.320, 1.275) all;
    -moz-transition: 0.3s cubic-bezier(0.175, 0.885, 0.320, 1.275) all;
    -ms-transition: 0.3s cubic-bezier(0.175, 0.885, 0.320, 1.275) all;
    transition: 0.3s cubic-bezier(0.175, 0.885, 0.320, 1.275) all;
}

.btn.btn-spinner {
    overflow: hidden;
    position: relative;
}

.btn.btn-spinner span {
    display: inherit;
    margin: inherit;
    color: inherit;
    font-weight: inherit;
    font-size: inherit;
}

.btn.btn-spinner .btn-label {
    position: relative;
    margin: 0;
}

.btn.btn-spinner .spinner {
    left: 50%;
    margin-left: -16px;
    margin-top: 1em;
}

.btn.btn-spinner[data-loading] .btn-label {
    opacity: 0;
    margin: 0;
}

.btn.btn-spinner[data-loading] .spinner {
    opacity: 1;
    margin-top: -16px;
}

.file-drop-loading-wrapper {
    position: absolute;
    left: 178px;
    top: 45px;
}

.file-drop-loading-wrapper div {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    float: left;
    margin: 0 3px;
    background: #E1F5F9;
    transform: scale(0);
}

.file-drop-loading-wrapper .file-drop-loading-ball1 {
    z-index: 1;
    -moz-animation: grow 4.4s infinite ease-in-out;
    -webkit-animation: grow 4.4s infinite ease-in-out;
    opacity: 0.8;
}

.file-drop-loading-wrapper .file-drop-loading-ball2 {
    -moz-animation: grow 4.4s infinite ease-in-out;
    -webkit-animation: grow 4.4s infinite ease-in-out;
    -webkit-animation-delay: 0.3s;
    animation-delay: 0.3s;
    opacity: 0.8;
}

.file-drop-loading-wrapper .file-drop-loading-ball3 {
    -moz-animation: grow 4.4s infinite ease-in-out;
    -webkit-animation: grow 4.4s infinite ease-in-out;
    -webkit-animation-delay: 0.6s;
    animation-delay: 0.6s;
    opacity: 0.8;
}

.file-drop-loading-wrapper .file-drop-loading-ball4 {
    -moz-animation: grow 4.4s infinite ease-in-out;
    -webkit-animation: grow 4.4s infinite ease-in-out;
    -webkit-animation-delay: 0.9s;
    animation-delay: 0.9s;
    opacity: 0.8;
}

@-moz-keyframes grow {
    0% {
        -moz-transform: scale(0);
    }

    25% {
        -moz-transform: scale(1);
    }

    50% {
        -moz-transform: scale(0);
    }

    75% {
        -moz-transform: scale(1);
        background: #72c2c2;
    }

    100% {
        -moz-transform: scale(0);
        background: #72c2c2;
    }
}

@-webkit-keyframes grow {
    0% {
        -webkit-transform: scale(0);
    }

    25% {
        -webkit-transform: scale(1);
    }

    50% {
        -webkit-transform: scale(0);
    }

    75% {
        -webkit-transform: scale(1);
        background: #72c2c2;
    }

    100% {
        -webkit-transform: scale(0);
        background: #72c2c2;
    }
}

.case-upload-file-list {
    margin-top: 19px;
    text-align: left;
}

.case-upload-file-list-item {
    border-bottom: 1px solid #E5E5E5;
    padding: 5px 0px 5px 0px;
}

.file-drop-area {
    border: 2px dashed #C4C4C4;
    border-radius: 0.75rem;
    position: relative;
    height: 117px;
    width: 480px;
    max-width: 100%;
    margin: 0 auto;
    padding: 26px 20px 30px;
    -webkit-transition: 0.2s;
    transition: 0.2s;
}

.file-drop-area.disabled {
    background: #fbfbfb;
    border: 2px dashed #dbdbdb;
    cursor: not-allowed;
}

.file-drop-area.is-active {
    background-color: #E1F5F9;
    border: 2px dashed #008489;
}

.file-drop-area.dropping {
    border: none;
}

.file-drop-mask {
    top: 0px;
    bottom: 0px;
    position: absolute;
    z-index: 99;
    margin: -2px;
    left: 0px;
    right: 0px;
}

.file-drop-msg {
    font-weight: 400;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 180px);
    vertical-align: middle;
    display: block;
    line-height: 60px;
    left: 121px;
}

.file-drop-msg.disabled {
    color: #343434;
}

.file-drop-msg-sub {
    font-weight: 400;
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 180px);
    vertical-align: middle;
    display: block;
    top: -25px;
    left: 163px;
    color: #343434;
}

.file-drop-msg-sub.disabled {
    color: #343434;
}

.file-drop-input {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    cursor: pointer;
    opacity: 0;
}

.file-drop-input:focus {
    outline: none;
}

.reference-info-container {
    width: 100%;
    display: flex;
    gap: 1rem;
    align-items: center;
    border-bottom: 1px solid $grey-3;

    h3, label {
        margin: 0;
    }

    label {
        margin-right: $padding-unit;
    }

    [class*="state-indicator-"] {
        width: unset;
        margin-bottom: 0;
        padding: 0.25rem $padding-unit;
        user-select: none;
        pointer-events: none;
    }
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto;
    gap: 1rem;
}

.badge-yes, .badge-no {
    text-align: center;
    padding: 0.25rem $padding-unit;
    border-radius: 4px;
    color: $white;
    width: 2.75rem;
}

.badge-yes {
    background-color: $success;
}

.badge-no {
    background-color: $active-alt-secondary;
}

// Contracts views
#edit-contract {
    .sub-header {
        h2 a {
            color: $grey-1;
        }

        h2 a:hover {
            color: $brand;
        }
    }

    .error-message {
        padding: 5px;
        color: red;
        margin-top: 5px;
        font-weight: 500;
    }

    .search-error-box {
        height: 175px;
        width: 100%;
        border: 1px solid #cacaca;
        border-radius: .5rem;
        padding: .6875rem;
        overflow-y: scroll;
        font: 13px Arial, Helvetica, Verdana, sans-serif;
    }
}

#contractDetails {
    .form-group {
        .contractHistoryLabel {
            font-weight: bold;
            margin-bottom: .4rem;
        }
    }
}

#help {
    section {
        border-radius: 5px;

        .files {
            .file {
                border: 1px solid $grey-3;
                border-radius: 5px;
                padding: 20px 20px 20px;
                width: 290px;

                span {
                    display: inline-block;
                    width: 75px;
                    font-weight: bold;
                }

                .file-buttons {
                    padding-top: 5px;
                    display: flex;
                    gap: $space-XS;
                }
            }
        }

        #build-info {
            p:last-of-type {
                margin-bottom: 0;
            }
        }
    }
}

@mixin display-box($size) {
    width: $size;
    height: $size;
    display: inline-block;
    box-sizing: border-box;
}

@mixin border($width, $colorTop, $colorRight, $colorBottom, $colorLeft) {
    border-top: solid $width $colorTop;
    border-right: solid $width $colorRight;
    border-bottom: solid $width $colorBottom;
    border-left: solid $width $colorLeft;
}

#preclassification {
    .locking-error-message, .in-progress-message {
        position: absolute;
        top: 123px;
        right: 28px;
        background: #FEFEFE;
        border-radius: 5px;
        padding: 5px 10px;
        min-width: 215px;
    }

    .locking-error-message {
        background: #FFE4E1;
        border-left: 2px solid #B22222;
        border-right: 2px solid #B22222;
    }

    .in-progress-message {
        $spinner-size: 16px !default;
        $spinner-color: $brand !default;
        $spinner-accent: $grey-3 !default;
        $speed2x: 2s;
        $speed3x: 1s;
        $speed4x: .5s;

        .spinner {
            @include display-box($spinner-size);
            position: relative;
            left: -3px;
            top: 2px;
        }

        .spinner.round {
            &::before {
                border-radius: 50%;
                content: " ";
                @include display-box($spinner-size);
                @include border(calc($spinner-size / 5), $spinner-accent, $spinner-accent, $spinner-accent, $spinner-accent);
                position: absolute;
                top: 0;
                left: 0;
            }

            &::after {
                border-radius: 50%;
                content: " ";
                @include display-box($spinner-size);
                @include border(calc($spinner-size / 5), $spinner-color, transparent, transparent, transparent);
                position: absolute;
                top: 0;
                left: 0;
                animation: round-animate $speed3x ease-in-out infinite;
            }
        }

        @keyframes round-animate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    }

    .modal-wrapper {

        .icon-button-cancel {
            display: none;
        }

        .dialog-custom-content {
            align-items: center;
        }

        #dialog-closer {
            display: none;
        }

        a.button.icon-button-tick {
            font-size: 0;
        }

        a.button.icon-button-tick:after {
            content: 'OK';
            font-size: initial;
        }
    }
}

#search {
    .classificationSearchFullTextErrorMessage {
        border: 1px solid #8A1A1E;
        border-radius: 6px;
        background: #F9E1E0;
        padding: 15px;
        margin: 13px 0;

        p {
            margin-bottom: 0
        }
    }
}

#reports {
    section {
        border-radius: 5px;

        .cards {
            display: flex;
            flex-wrap: wrap;
            column-gap: 20px;
            row-gap: 10px;

            .card {
                border: 1px solid $grey-3;
                border-radius: 5px;
                width: 290px;
                padding: 0;
                margin: 0;
                display: flex;
                flex-direction: column;

                .card-info {
                    padding: 20px 20px 10px;
                    flex: 1;

                    h2 {
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 3;
                        overflow: hidden;
                    }
                }

                .card-buttons {
                    border-top: 1px solid $grey-3;
                    padding: 10px 20px 10px;

                    .button {
                        margin-right: 15px;
                    }
                }
            }
        }
    }
}


#importcards {
    section {
        border-radius: 5px;

        .cards {
            display: flex;
            flex-wrap: wrap;
            column-gap: 20px;
            row-gap: 10px;

            .card {
                border: 1px solid $grey-3;
                border-radius: 5px;
                width: 390px;
                height: 180px;
                padding: 0;
                margin: 0;
                display: flex;
                flex-direction: column;

                .card-info {
                    flex: 1;

                    h2 {
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 3;
                        overflow: hidden;
                    }
                }

                .card-buttons {
                    border-top: 1px solid $grey-3;
                    padding: 10px 20px 10px;

                    .button {
                        margin-right: 15px;
                    }
                }
            }
        }

        .import-card-type {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .import-card-title-text {
            color: #461E96;
            font-size: 17px;
            font-weight: bold;
            padding: 10px 20px 0px;
        }

        .import-pubmed-card-contract-text {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 0px 20px 0px;
            min-height: 50px
        }

        .import-card-contract-text {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 0px 20px 0px;
        }

        .import-card-date-title {
            color: #8a8a8a;
            font-size: 12px;
            padding: 10px 12px 0px;
        }

        .import-card-date {
            font-size: 12px;
            padding: 0px 12px 0px;
        }

        .import-card-updatedby-title {
            color: #8a8a8a;
            font-size: 12px;
            padding: 10px 12px 0px;
        }

        .import-card-updatedby {
            font-size: 12px;
            padding: 0px 12px 0px;
            width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .import-card-createdby {
            font-size: 12px;
            padding: 0px 12px 0px;
            width: 170px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .import-card-journal-title {
            font-weight: bold;
            padding: 0px 20px 0px;
        }

        .import-card-text {
            font-size: 12px;
            width: 400px;
            padding: 0px 20px 0px;
            word-break: break-word;
            min-height: 40px;
        }

        .import-card-mod {
            display: flex;
            padding: 0px 10px 0px;
        }

        .import-card-line {
            display: flex;
            border-top: 1px solid $grey-3;
            padding: 0px 10px 0px;
        }

        .import-card-mod-date {
            display: flex;
            flex-direction: column;
            margin-right: 15px;
        }

        .import-card-title {
            width: 100px;
            height: 30px;
        }

        .import-card-file-import-lozenge {
            background-color: #E7F7FF;
            padding: 2px 10px 2px 10px;
            margin-right: 5px;
            border-radius: 15px;
            border-color: #CFEFFE;
            border-style: solid;
            border-width: 3px;
            margin-bottom: 2px;
        }


        .import-card-failed-lozenge {
            padding: 2px 10px 2px 10px;
            margin-right: 5px;
            border-radius: 15px;
            margin-top: 8px;


            &.status-Failed {
                background-color: $red-light;
                color: $red-400;
            }

            &.status-Reviewed {
                background-color: $yellow-100;
                display: flex;
                align-items: center;
                gap: 4px;

                .m-icon.info-circle {
                    width: auto;
                    font-size: 16px;
                    line-height: 1;
                }
            }
        }
    }
}

.fileimportbutton {
    height: 40px;
    margin-left: 10px;
    padding-left: 10px;
    line-height: 25px;
    text-align: center
}

.ellipsis-button {
    border: 2px solid;
    border-color: #e5e5e5;
    border-radius: 4px;
    height: 25px;
    position: relative;
    color: white;
}

.ellipsis-button:hover {
    color: white;
    background-color: #ededed;
}

.ellipsis-button > a > .nav-menu-user-icon {
    margin-right: initial;
}

.ellipsis-dropdown-content {
    display: none;
    flex-direction: column;
    position: absolute;
    background-color: white;
    min-width: 90px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;

    ul > li {
        margin-bottom: initial;
    }
}

.ellipsis-dropdown-content > ul > li:hover {
    background-color: #ddd;
    cursor: pointer;
}

.ellipsis-container {
    position: relative;
    padding: 8px;
}

.ellipsis-container:hover .ellipsis-dropdown-content {
    display: flex;
    flex-direction: column;
}

.ellipsis-dropdown-content > ul > li:nth-child(1) {
    margin-top: 10px;
    padding-left: 15px;
}

.ellipsis-dropdown-content > ul li:nth-child(2) {
    margin-bottom: 10px;
    padding-left: 15px;
}

#tracking-sheets {
    section {
        border-radius: 5px;

        .filters {
            position: absolute;
            top: 150px;
            right: 80px;

            select#company {
                width: 250px;
            }

            select#year {
                width: 90px;
            }
        }

        .cards {
            display: flex;
            flex-wrap: wrap;
            column-gap: 10px;
            margin: 10px auto;

            .card {
                width: 140px;
                padding: 0;
                margin: 0 0 10px;

                .card-info {
                    padding: 10px 10px 10px;
                    background: #E3F2FD;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;

                    h2 {
                        color: #1F394A;
                        font-size: 24px;
                        padding: 5px 0 0;
                    }

                    p {
                        color: #1F394A;
                        font-size: 13px;
                        margin: 0;
                    }
                }

                .card-buttons {
                    padding: 10px 10px 10px;
                    background: #CCE6F8;
                    border-bottom-left-radius: 5px;
                    border-bottom-right-radius: 5px;
                    font-size: 13px;

                    a {
                        color: #01699f;
                        margin-right: 15px;
                    }
                }

                &.future {
                    .card-info {
                        background: #F3F6F9;

                        h2, p {
                            color: #E5E5E5;
                        }
                    }

                    .card-buttons {
                        background: #E5E5E5;
                    }
                }

                &.missing {
                    .card-info {
                        background: #F8E5C4;

                        h2, p {
                            color: #1F394A;
                        }
                    }

                    .card-buttons {
                        background: #F0DF9D;

                        a {
                            color: #1F394A;
                        }
                    }
                }
            }
        }
    }
}

#split-references {
    // Split references page
    .company-multi-select {
        height: 60px;
    }
}

#apogepha-client-report, #merz-client-report, #accovion-client-report {
    section {
        border-radius: 5px;

        .filters {
            position: absolute;
            top: 150px;
            right: 80px;

            select#year {
                width: 90px;
            }
        }

        .cards {
            display: flex;
            flex-wrap: wrap;
            column-gap: 10px;
            margin: 10px auto;

            .card {
                width: 140px;
                padding: 0;
                margin: 0 0 10px;

                .card-info {
                    padding: 10px 10px 10px;
                    background: #E3F2FD;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;

                    h2 {
                        color: #1F394A;
                        font-size: 18px;
                        padding: 5px 0 0;
                    }

                    p {
                        color: #1F394A;
                        font-size: 13px;
                        margin: 0;
                    }
                }

                .card-buttons {
                    padding: 10px 10px 10px;
                    background: #CCE6F8;
                    border-bottom-left-radius: 5px;
                    border-bottom-right-radius: 5px;
                    font-size: 13px;

                    a {
                        color: #01699f;
                        margin-right: 15px;
                    }
                }

                &.future {
                    .card-info {
                        background: #F3F6F9;

                        h2, p {
                            color: #E5E5E5;
                        }
                    }

                    .card-buttons {
                        background: #E5E5E5;
                    }
                }
            }
        }
    }
}

.custom-select {
    &.small {
        &:before {
            top: 9px;
        }
    }
}

button.secondary:disabled,
button.secondary.disabled,
a.button.secondary:disabled,
a.button.secondary.disabled {
    border-color: $grey-3;
    color: $grey-3;
}

.nav-menu > li .flyout {
    top: 51px;
    left: 1px;
}
//Settings page styling
#settings {

    .global-container {
        display: flex;
    }

    .sidebar {
        width: 200px;
        margin-top: 10px;
        margin-right: 50px;
    }

    .main-content {
        flex-grow: 2
    }

    .menu {
        li {
            padding: 5px;
            margin-right: 5px;

            &:hover {
                background-color: #e7f7ff;
            }

            &.active {
                background-color: #e7f7ff;
                font-weight: 600;
            }
        }

        a {
            text-decoration: none;
        }
    }
    // Journals-tables-template styling
    #journals-table {
        .sub-header {
            border: none;
            background: none;

            h2 {
                font-weight: 700;
                margin-left: 10px;
                font-size: 1.2rem;
            }
        }
    }
    // Journal-modal-template styling
    #journal-modal {
        .modal-wide {
            max-width: 40%;
            width: 40%;

            .modal-header {
                h2 {
                    font-weight: 600;
                }

                button {
                    font-size: 1.5rem;
                    color: black;
                    background: none;
                }
            }

            .modal-body {
                padding: 0 20px 10px;

                .flex {
                    .flex-item:nth-child(2) {
                        flex-grow: 2;
                    }
                }

                .error-color {
                    font-weight: 600;
                }
            }

            .modal-footer {
                .button {
                    margin-right: 5px;
                }
            }
        }
    }

    .ai-suggestion-toggle-panel {
        background-color: #EEE;
        padding: 1rem;
        max-width: 800px;

        h2, p {
            margin-left: 15px;
        }
    }
}

.ai-retry {
    button {
        width: 100%;
        margin-bottom: 8.0rem;
    }
}

#ad-hoc-list {
    select {
        background-color: $blue-dark;
        color: #ffffff;

        option:disabled {
            background-color: $grey-4;
        }

        option {
            background-color: #ffffff;
            color: #333;
            padding: 8px;
        }
    }
}

#manual-entry-modal {
    .modal-wide {
        max-width: 70%;
        width: 70%;

        .modal-body {
            max-height: none;
            overflow-y: visible;
            padding-left: 1.2rem;
            padding-right: 1.2rem;

            #ad-hoc-manual-entry-form {
                .flex {
                    gap: 10px;
                }

                .flex-item:nth-child(2) {
                    flex-grow: 2;
                }

                .flex-item {
                    flex-grow: 1;
                    display: flex;
                    flex-direction: column;
                }

                .form-group {
                    flex: 1;
                    display: flex;
                    flex-direction: column;

                    textarea {
                        flex: 1;
                    }
                }

                h1 {
                    padding: 20px;
                    font-weight: 500;
                }

                .doi-message {
                    padding: 5px;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    margin-top: 5px;
                }

                #doi-exist-message,
                #doi-invalid-message {
                    background-color: $red-light;
                }

                #valid-doi-message {
                    background-color: $green-300;

                    p, i.m-icon {
                        padding-left: 3px;
                        font-weight: 500;
                        display: inline-block;
                        vertical-align: middle;
                        margin: 0;
                        pointer-events: none;
                        cursor: default;
                    }
                }

                .error-message {
                    padding: 5px;
                    color: red;
                    margin-top: 5px;
                    font-weight: 500;
                }

                .error-field-highlight {
                    border-color: red;
                    border-width: 2px;
                }

                .error-label {
                    color: red;
                }

                .error-summary-panel {
                    background-color: #FCD9D1;
                    color: #984937;
                    border-radius: .5rem;
                    padding: 10px;
                    margin-bottom: 15px;
                    white-space: pre-line;
                }

                #sources-wrapper {
                    margin: 15px;
                    border: 1px solid $grey-4;
                    border-radius: 10px;

                    h2 {
                        padding-left: 25px;
                        margin-top: 20px;
                    }

                    .form-group {
                        padding-left: 20px;
                        padding-top: 10px;
                    }
                }
            }
        }

        .modal-footer {
            .button {
                margin-right: 5px;
            }
        }
    }
}

#file-import-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .button-container {
        display: flex;
        justify-content: flex-end;
        width: 100%;
        margin-top: 10px;
    }

    .button {
        margin-right: 10px;
    }
}

.scheduler-settings-container {
    border-radius: 8px;
    padding: 20px;

    .schedule-section {
        border-radius: 6px;
        padding: 16px;
        background:$grey-ultra-light;
        border: 1px solid $grey-6;

        .section-title {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: $black;
        }

        .repeat-controls {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .repeat-label {
                font-size: 14px;
                font-weight: 600;
                color: $black;
            }

            .repeat-input-group {
                display: flex;
                gap: 8px;
                align-items: center;

                .repeat-number-input {
                    width: 60px;
                    padding: 8px 12px;
                    border: 1px solid $grey-4;
                    border-radius: 4px;
                    font-size: 14px;
                    text-align: center;

                    &:focus {
                        outline: none;
                        border-color: $brand;
                    }
                }

                .repeat-unit-select {
                    padding: 8px 12px;
                    border: 1px solid $grey-4;
                    border-radius: 4px;
                    font-size: 14px;
                    background: $white;
                    cursor: pointer;
                    min-width: 80px;
                    max-width: 150px;

                    &:focus {
                        outline: none;
                        border-color: $brand;
                    }
                }
            }
        }

        .day-selection {
            margin-top: 15px;

            .day-selection-header {
                margin-bottom: 10px;

                .day-selection-label {
                    font-weight: 700;
                    color: $brand;
                    font-size: 16px;
                }
            }

            .day-buttons {
                display: flex;
                gap: 5px;
                flex-wrap: wrap;

                .day-button {
                    padding: 8px 12px;
                    border: 1px solid $grey-2;
                    background: $white;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                    color: $grey-2;
                    transition: all 0.2s ease;
                    min-width: 42px;
                    text-align: center;

                    &:hover {
                        background: $grey-3;
                        border-color: $grey-3;
                    }

                    &.active {
                        background: $brand;
                        color: white;
                        border-color: $brand;

                        &:hover {
                            background: #5a4fcf;
                            border-color: #5a4fcf;
                        }
                    }
                }
            }

            .monthly-options {
                margin-top: 10px;

                label {
                    padding-top: 5px;
                    font-size: 14px;
                    font-weight: 600;
                }

                .monthly-date-selection,
                .monthly-weekday-selection {
                    margin-top: 15px;
                }

                .weekday-controls {
                    display: flex;
                    gap: 10px;
                    align-items: center;

                    .week-occurrence-select,
                    .weekday-select {
                        padding: 8px;
                        border: 1px solid $grey-4;
                        border-radius: 4px;
                        background: $white;
                        font-size: 13px;
                    }

                    .week-occurrence-select {
                        min-width: 70px;
                    }

                    .weekday-select {
                        min-width: 100px;
                    }
                }
            }
        }
    }
}
