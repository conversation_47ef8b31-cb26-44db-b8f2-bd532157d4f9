﻿<Project Sdk="Microsoft.NET.Sdk">


  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>
	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1859</NoWarn>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.ReferenceManagement.Contracts\PharmaLex.VigiLit.ReferenceManagement.Contracts.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.ReferenceManagement.Service\PharmaLex.VigiLit.ReferenceManagement.Service.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.ReferenceManagement\PharmaLex.VigiLit.ReferenceManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Test.Fakes\PharmaLex.VigiLit.Test.Fakes.csproj" />
  </ItemGroup>

</Project>
