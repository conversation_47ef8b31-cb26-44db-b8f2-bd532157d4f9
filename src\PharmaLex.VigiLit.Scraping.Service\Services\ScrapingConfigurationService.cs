using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ScrapingConfigurationService : IScrapingConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ScrapingConfigurationService> _logger;

    public ScrapingConfigurationService(IConfiguration configuration, ILogger<ScrapingConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public string GetWebhookUrl()
    {
        var baseUrl = "https://localhost:5001/"; //_configuration["HostUri"];
        //if (string.IsNullOrEmpty(baseUrl))
        //{
        //    _logger.LogWarning("No base URL configured for webhooks");
        //    return string.Empty;
        //}

        var webhookPath = "/api/apify/webhook";
        return $"{baseUrl.TrimEnd('/')}{webhookPath}";
    }
}
