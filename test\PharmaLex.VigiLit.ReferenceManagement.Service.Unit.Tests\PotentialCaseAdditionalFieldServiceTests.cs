﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using Xunit;

namespace PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests;

public class PotentialCaseAdditionalFieldServiceTests
{
    private readonly IPotentialCaseAdditionalFieldService _potentialCaseAdditionalFieldService;
    private readonly Mock<IPotentialCaseAdditionalFieldRepository> _mockPotentialCaseAdditionalFieldRepository = new();

    public PotentialCaseAdditionalFieldServiceTests()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PotentialCaseAdditionalFieldMappingProfile>();
        });
        var mapper = config.CreateMapper();

        _potentialCaseAdditionalFieldService = new PotentialCaseAdditionalFieldService(_mockPotentialCaseAdditionalFieldRepository.Object, mapper);
    }

    [Fact]
    public async Task GetAllAsync_returns_list_of_PotentialCaseAdditionalFields() // Only seems to test the mapper?
    {
        // Arrange
        _mockPotentialCaseAdditionalFieldRepository.Setup(x => x.GetAllAsync())
            .ReturnsAsync(GetPotentialCaseAdditionalFields());

        // Act
        var result = await _potentialCaseAdditionalFieldService.GetAllAsync();

        // Assert
        Assert.Equal(5, result.Count());
    }

    private List<PotentialCaseAdditionalField> GetPotentialCaseAdditionalFields()
    {
        return
        [
            new FakePotentialCaseAdditionalField(1) { Name = "Field 1" },
            new FakePotentialCaseAdditionalField(2) { Name = "Field 2" },
            new FakePotentialCaseAdditionalField(3) { Name = "Field 3" },
            new FakePotentialCaseAdditionalField(4) { Name = "Field 4" },
            new FakePotentialCaseAdditionalField(5) { Name = "Field 5" }
        ];
    }
}